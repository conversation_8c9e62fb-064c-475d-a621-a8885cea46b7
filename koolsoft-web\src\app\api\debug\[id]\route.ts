import { NextRequest, NextResponse } from 'next/server';
import { getCustomerRepository, getHistoryCardRepository, getLegacyDataRepository } from '@/lib/repositories';
import { withRoleProtection } from '@/lib/auth/middleware';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * GET /api/debug/[id]
 * Debug endpoint to examine customer data
 */
async function debugCustomer(
  request: NextRequest,
  context?: any
) {
  try {
    // Extract ID from URL path
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    // ID is last part in /debug/[id]
    const id = pathParts[pathParts.length - 1];

    const customerRepository = getCustomerRepository();
    const historyCardRepository = getHistoryCardRepository();
    const legacyDataRepository = getLegacyDataRepository();

    // Get customer with all related data
    const customer = await customerRepository.findWithRelations(id);

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      );
    }

    // Get history cards for this customer
    let historyCards = [];
    try {
      historyCards = await historyCardRepository.findByCustomerId(id, 0, 100);
    } catch (error) {
      console.error('Error fetching history cards:', error);
    }

    // Get history cards with complaints directly from the database
    let historyCardsWithComplaints = [];
    try {
      historyCardsWithComplaints = await legacyDataRepository.getCustomerHistoryCardsWithComplaints(id);
    } catch (error) {
      console.error('Error fetching history cards with complaints:', error);
    }

    // Get complaints directly from the database
    let directComplaints = [];
    try {
      directComplaints = await prisma.$queryRaw`
        SELECT * FROM history_complaints
        WHERE history_card_id IN (
          SELECT id FROM history_cards
          WHERE customer_id = ${id}
        )
      `;
    } catch (error) {
      console.error('Error fetching direct complaints:', error);
    }

    // Check for legacy complaints
    let legacyComplaints = [];
    try {
      const legacyTableExists = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'History_Complaint'
        ) AS table_exists
      `;

      if (legacyTableExists[0].table_exists) {
        legacyComplaints = await prisma.$queryRaw`
          SELECT hc.*, c.* 
          FROM "History_Complaint" hc
          JOIN "CUSTOMERS" c ON hc."Customer_ID" = c."ID"
          WHERE c."ID" = ${customer.originalId}
          LIMIT 10
        `;
      }
    } catch (error) {
      console.error('Error fetching legacy complaints:', error);
    }

    // Format response
    const response = {
      customer: {
        id: customer.id,
        name: customer.name,
        address: customer.address,
        originalId: customer.originalId,
      },
      contacts: customer.contacts,
      historyCards,
      historyCardsWithComplaints,
      directComplaints,
      legacyComplaints,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error debugging customer:', error);
    return NextResponse.json(
      { error: 'Failed to debug customer', details: error },
      { status: 500 }
    );
  }
}

// Export handler with role protection
export const GET = withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], debugCustomer);
