import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getHistoryCardRepository } from '@/lib/repositories';
import { ActivityLoggerMiddlewareFactory } from '@/lib/middleware/activity-logger.middleware';

// Validation schemas for bulk operations
const bulkDeleteSchema = z.object({
  action: z.literal('delete'),
  ids: z.array(z.string().uuid()).min(1, 'At least one ID is required'),
});

const bulkUpdateSchema = z.object({
  action: z.literal('update'),
  ids: z.array(z.string().uuid()).min(1, 'At least one ID is required'),
  data: z.object({
    source: z.enum(['AMC', 'INW', 'OTW']).optional(),
    amcId: z.string().uuid().optional(),
    inWarrantyId: z.string().uuid().optional(),
    outWarrantyId: z.string().uuid().optional(),
  }).refine(data => Object.keys(data).length > 0, {
    message: 'At least one field must be provided for update',
  }),
});

const bulkOperationSchema = z.discriminatedUnion('action', [
  bulkDeleteSchema,
  bulkUpdateSchema,
]);

/**
 * POST /api/history-cards/bulk
 * Perform bulk operations on history cards (delete, update)
 */
async function bulkOperations(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = bulkOperationSchema.parse(body);
    
    const historyCardRepository = getHistoryCardRepository();
    
    if (validatedData.action === 'delete') {
      // Bulk delete operation
      const { ids } = validatedData;
      
      // Check if all history cards exist
      const existingCards = await (historyCardRepository as any).model.findMany({
        where: {
          id: {
            in: ids,
          },
        },
        select: {
          id: true,
        },
      });
      
      const existingIds = existingCards.map(card => card.id);
      const notFoundIds = ids.filter(id => !existingIds.includes(id));
      
      if (notFoundIds.length > 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'Some history cards not found',
            details: {
              notFound: notFoundIds,
            },
          },
          { status: 404 }
        );
      }
      
      // Perform bulk delete
      const deleteResult = await (historyCardRepository as any).model.deleteMany({
        where: {
          id: {
            in: ids,
          },
        },
      });
      
      return NextResponse.json({
        success: true,
        message: `Successfully deleted ${deleteResult.count} history cards`,
        data: {
          deletedCount: deleteResult.count,
          deletedIds: ids,
        },
      });
    } else if (validatedData.action === 'update') {
      // Bulk update operation
      const { ids, data } = validatedData;
      
      // Check if all history cards exist
      const existingCards = await (historyCardRepository as any).model.findMany({
        where: {
          id: {
            in: ids,
          },
        },
        select: {
          id: true,
        },
      });
      
      const existingIds = existingCards.map(card => card.id);
      const notFoundIds = ids.filter(id => !existingIds.includes(id));
      
      if (notFoundIds.length > 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'Some history cards not found',
            details: {
              notFound: notFoundIds,
            },
          },
          { status: 404 }
        );
      }
      
      // Perform bulk update
      const updateResult = await (historyCardRepository as any).model.updateMany({
        where: {
          id: {
            in: ids,
          },
        },
        data,
      });
      
      return NextResponse.json({
        success: true,
        message: `Successfully updated ${updateResult.count} history cards`,
        data: {
          updatedCount: updateResult.count,
          updatedIds: ids,
          updateData: data,
        },
      });
    }
    
    // This should never be reached due to discriminated union validation
    return NextResponse.json(
      {
        success: false,
        error: 'Invalid bulk operation',
      },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error performing bulk operation:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to perform bulk operation',
      },
      { status: 500 }
    );
  }
}

// Export handler with role protection and activity logging
export const POST = ActivityLoggerMiddlewareFactory.forHistoryCardRoutes(
  withRoleProtection(['ADMIN', 'MANAGER'], bulkOperations),
  {
    action: 'bulk_operation_history_cards',
    getEntityId: (req) => {
      try {
        // For bulk operations, we'll log the action type
        return 'bulk';
      } catch (error) {
        console.error('Error getting entity ID:', error);
        return 'unknown';
      }
    },
  }
);
