import { NextRequest, NextResponse } from 'next/server';
import { getCustomerRepository } from '@/lib/repositories';
import { z } from 'zod';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ActivityLoggerMiddlewareFactory } from '@/lib/middleware/activity-logger.middleware';

/**
 * Customer creation schema
 */
const createCustomerSchema = z.object({
  name: z.string().min(2).max(100),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pinCode: z.string().optional(),
  phone: z.string().optional(),
  phone1: z.string().optional(),
  phone2: z.string().optional(),
  phone3: z.string().optional(),
  fax: z.string().optional(),
  email: z.string().email().optional(),
  mobile: z.string().optional(),
  website: z.string().url().optional(),
  location: z.string().optional(),
  isActive: z.boolean().default(true),
  contacts: z.array(
    z.object({
      name: z.string().min(2).max(100),
      designation: z.string().optional(),
      phone: z.string().optional(),
      email: z.string().email().optional(),
      isPrimary: z.boolean().default(false),
    })
  ).optional(),
});

/**
 * GET /api/customers
 * Get all customers with optional pagination
 */
async function getCustomers(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get('skip') || '0');
    const take = parseInt(searchParams.get('take') || '10');
    const search = searchParams.get('search') || '';

    const customerRepository = getCustomerRepository();

    let customers = [];
    let total = 0;

    if (search) {
      // Search by name, email, or phone
      const nameResults = await customerRepository.findByName(search, skip, take);
      const emailResults = await customerRepository.findByEmail(search, 0, take);
      const phoneResults = await customerRepository.findByPhone(search, 0, take);

      // Combine results and remove duplicates
      const allResults = [...nameResults, ...emailResults, ...phoneResults];
      const uniqueIds = new Set();
      customers = allResults.filter(customer => {
        if (uniqueIds.has(customer.id)) {
          return false;
        }
        uniqueIds.add(customer.id);
        return true;
      }).slice(skip, skip + take);

      total = uniqueIds.size;
    } else {
      customers = await customerRepository.findAll(skip, take);
      total = await customerRepository.count();
    }

    return NextResponse.json({
      success: true,
      data: {
        customers,
      },
      meta: {
        total,
        skip,
        take,
      },
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customers' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/customers
 * Create a new customer with contacts in a transaction
 */
async function createCustomer(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Customer creation request body:', body);

    // Validate request body
    try {
      const validatedData = createCustomerSchema.parse(body);
      console.log('Validated data:', validatedData);

      const customerRepository = getCustomerRepository();

      // Extract contacts from validated data
      const { contacts, ...customerData } = validatedData;

    // Use transaction to create customer and contacts
    const customer = await customerRepository.transaction(async (txRepo) => {
      // Create customer
      const newCustomer = await txRepo.create(customerData);

      // Create contacts if provided
      if (contacts && contacts.length > 0) {
        // Use direct prisma access
        for (const contact of contacts) {
          await (txRepo as any).prisma.contact.create({
            data: {
              ...contact,
              customerId: newCustomer.id,
            },
          });
        }
      }

      // Return customer with contacts
      return (txRepo as any).prisma.customer.findUnique({
        where: { id: newCustomer.id },
        include: { contacts: true },
      });
    });

    return NextResponse.json(customer, { status: 201 });
    } catch (error) {
      // Handle validation errors
      if (error instanceof z.ZodError) {
        console.error('Validation error:', error.errors);
        return NextResponse.json(
          {
            error: 'Validation error',
            details: error.errors,
            message: error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
          },
          { status: 400 }
        );
      }
      throw error; // Re-throw for outer catch block
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.errors);
      return NextResponse.json(
        {
          error: 'Validation error',
          details: error.errors,
          message: error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    console.error('Error creating customer:', error);
    return NextResponse.json(
      { error: 'Failed to create customer', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Export handlers with role protection and activity logging
export const GET = ActivityLoggerMiddlewareFactory.forCustomerRoutes(
  withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], getCustomers),
  {
    action: 'view_customers',
    getEntityId: () => null, // No specific entity ID for list view
  }
);

export const POST = ActivityLoggerMiddlewareFactory.forCustomerRoutes(
  withRoleProtection(['ADMIN', 'MANAGER'], createCustomer),
  {
    action: 'create_customer',
    getEntityId: (_, res) => {
      try {
        // Extract the customer ID from the response
        if (res && res.body) {
          const body = JSON.parse(res.body.toString());
          return body.id;
        }
      } catch (error) {
        console.error('Error extracting customer ID for activity log:', error);
      }
      return null;
    },
  }
);
