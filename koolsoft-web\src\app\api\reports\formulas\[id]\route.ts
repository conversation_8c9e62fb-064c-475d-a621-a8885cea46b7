/**
 * Individual Formula API Routes
 * 
 * Handles operations for specific formulas including get, update, and delete.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { z } from 'zod';
import { authOptions } from '@/lib/auth';
import { withRoleProtection } from '@/lib/auth/middleware';
import { FormulaRepository, updateFormulaSchema } from '@/lib/repositories/formula.repository';

const formulaRepository = new FormulaRepository();

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/reports/formulas/[id]
 * Get formula by ID
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formula = await formulaRepository.getFormulaById(params.id);

    if (!formula) {
      return NextResponse.json(
        { error: 'Formula not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: formula,
    });
  } catch (error) {
    console.error('Error fetching formula:', error);
    return NextResponse.json(
      { error: 'Failed to fetch formula' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/reports/formulas/[id]
 * Update formula
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  return withRoleProtection(['ADMIN', 'MANAGER'], async (session) => {
    try {
      const body = await request.json();
      const validatedData = updateFormulaSchema.parse(body);

      // Check if formula exists
      const existingFormula = await formulaRepository.getFormulaById(params.id);
      if (!existingFormula) {
        return NextResponse.json(
          { error: 'Formula not found' },
          { status: 404 }
        );
      }

      // Check permissions (only creator or admin can update)
      if (existingFormula.createdBy !== session.user.id && 
          !['ADMIN'].includes(session.user.role?.toUpperCase() || '')) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        );
      }

      const updatedFormula = await formulaRepository.updateFormula(
        params.id,
        validatedData
      );

      return NextResponse.json({
        success: true,
        data: updatedFormula,
        message: 'Formula updated successfully',
      });
    } catch (error) {
      console.error('Error updating formula:', error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: 'Invalid formula data', details: error.errors },
          { status: 400 }
        );
      }

      if ((error as any).code === 'P2002') {
        return NextResponse.json(
          { error: 'Formula with this name already exists' },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to update formula' },
        { status: 500 }
      );
    }
  })(request);
}

/**
 * DELETE /api/reports/formulas/[id]
 * Delete formula
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  return withRoleProtection(['ADMIN', 'MANAGER'], async (session) => {
    try {
      // Check if formula exists
      const existingFormula = await formulaRepository.getFormulaById(params.id);
      if (!existingFormula) {
        return NextResponse.json(
          { error: 'Formula not found' },
          { status: 404 }
        );
      }

      // Check permissions (only creator or admin can delete)
      if (existingFormula.createdBy !== session.user.id && 
          !['ADMIN'].includes(session.user.role?.toUpperCase() || '')) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        );
      }

      // Check if formula is being used in reports
      if (existingFormula.reportFields.length > 0) {
        return NextResponse.json(
          { 
            error: 'Cannot delete formula that is being used in reports',
            details: `Formula is used in ${existingFormula.reportFields.length} report field(s)`
          },
          { status: 409 }
        );
      }

      await formulaRepository.deleteFormula(params.id);

      return NextResponse.json({
        success: true,
        message: 'Formula deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting formula:', error);
      return NextResponse.json(
        { error: 'Failed to delete formula' },
        { status: 500 }
      );
    }
  })(request);
}
