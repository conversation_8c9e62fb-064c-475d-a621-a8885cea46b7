import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { ReportEmailConfigRepository } from '@/lib/repositories/email-distribution.repository';
import {
  createReportEmailConfigSchema,
  listReportEmailConfigsSchema,
} from '@/lib/validations/email-distribution.schema';
import { z } from 'zod';

/**
 * GET /api/reports/email/configs
 * List email configurations
 */
export const GET = withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], async (request: NextRequest, { user }) => {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      reportType: searchParams.get('reportType') || undefined,
      search: searchParams.get('search') || undefined,
      isActive: searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined,
    };

    const validatedParams = listReportEmailConfigsSchema.parse(queryParams);
    const repository = new ReportEmailConfigRepository();

    let result;
    if (validatedParams.reportType) {
      result = await repository.findByReportType(validatedParams.reportType, validatedParams);
    } else {
      // Get all configurations with pagination
      const whereClause: any = {};
      
      if (validatedParams.isActive !== undefined) {
        whereClause.isActive = validatedParams.isActive;
      }

      if (validatedParams.search) {
        whereClause.OR = [
          { name: { contains: validatedParams.search, mode: 'insensitive' } },
          { description: { contains: validatedParams.search, mode: 'insensitive' } },
        ];
      }

      const skip = (validatedParams.page - 1) * validatedParams.limit;

      const [data, total] = await Promise.all([
        repository.getModel().findMany({
          where: whereClause,
          skip,
          take: validatedParams.limit,
          orderBy: { name: 'asc' },
          include: {
            user: {
              select: { id: true, name: true, email: true },
            },
            emailTemplate: {
              select: { id: true, name: true, subject: true },
            },
            distributionList: {
              select: { id: true, name: true, emails: true },
            },
            _count: {
              select: { emailDeliveries: true },
            },
          },
        }),
        repository.getModel().count({ where: whereClause }),
      ]);

      result = {
        data,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total,
          totalPages: Math.ceil(total / validatedParams.limit),
        },
      };
    }

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });

  } catch (error) {
    console.error('Error fetching email configurations:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid parameters', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch email configurations' 
      },
      { status: 500 }
    );
  }
});

/**
 * POST /api/reports/email/configs
 * Create a new email configuration
 */
export const POST = withRoleProtection(['ADMIN', 'MANAGER'], async (request: NextRequest, { user }) => {
  try {
    const body = await request.json();
    const validatedData = createReportEmailConfigSchema.parse(body);

    const repository = new ReportEmailConfigRepository();

    // Check if name already exists for this report type
    const existing = await repository.getModel().findFirst({
      where: {
        name: validatedData.name,
        reportType: validatedData.reportType,
      },
    });

    if (existing) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email configuration with this name already exists for this report type' 
        },
        { status: 409 }
      );
    }

    // Validate that distribution list exists if provided
    if (validatedData.distributionListId) {
      const distributionListExists = await repository.getPrismaClient().email_distribution_lists.findFirst({
        where: { 
          id: validatedData.distributionListId,
          isActive: true,
        },
      });

      if (!distributionListExists) {
        return NextResponse.json(
          { success: false, error: 'Distribution list not found or inactive' },
          { status: 400 }
        );
      }
    }

    // Validate that email template exists if provided
    if (validatedData.emailTemplateId) {
      const templateExists = await repository.getPrismaClient().emailTemplate.findFirst({
        where: { 
          id: validatedData.emailTemplateId,
          isActive: true,
        },
      });

      if (!templateExists) {
        return NextResponse.json(
          { success: false, error: 'Email template not found or inactive' },
          { status: 400 }
        );
      }
    }

    const emailConfig = await repository.create({
      ...validatedData,
      createdBy: user.id,
    });

    return NextResponse.json({
      success: true,
      data: emailConfig,
      message: 'Email configuration created successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating email configuration:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data', 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create email configuration' 
      },
      { status: 500 }
    );
  }
});
