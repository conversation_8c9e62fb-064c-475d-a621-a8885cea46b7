/**
 * History Overview API Routes
 * 
 * This file handles API routes for comprehensive history overview.
 * It provides endpoints for getting customer history summaries and statistics.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withRoleProtection } from '@/lib/auth/middleware';
import { HistoryCardRepository } from '@/lib/repositories/history-card.repository';
import { HistoryRepairRepository } from '@/lib/repositories/history-repair.repository';
import { ActivityLoggerMiddlewareFactory } from '@/lib/middleware/activity-logger.middleware';

// Initialize repositories
const historyCardRepository = new HistoryCardRepository();
const historyRepairRepository = new HistoryRepairRepository();

// Validation schema for overview query parameters
const overviewQuerySchema = z.object({
  customerId: z.string().uuid('Valid customer ID is required'),
  includeStatistics: z.coerce.boolean().default(true),
  includeRecentActivity: z.coerce.boolean().default(true),
  activityLimit: z.coerce.number().int().min(1).max(50).default(10),
});

/**
 * GET /api/history/overview
 * Get comprehensive history overview for a customer
 */
async function getHistoryOverview(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Validate query parameters
    const queryParams = overviewQuerySchema.parse({
      customerId: searchParams.get('customerId'),
      includeStatistics: searchParams.get('includeStatistics'),
      includeRecentActivity: searchParams.get('includeRecentActivity'),
      activityLimit: searchParams.get('activityLimit'),
    });

    // Get customer history overview
    const overview = await historyCardRepository.getCustomerHistoryOverview(
      queryParams.customerId
    );

    if (!overview) {
      return NextResponse.json(
        {
          success: false,
          error: 'Customer not found',
        },
        { status: 404 }
      );
    }

    // Get additional statistics if requested
    let statistics = null;
    if (queryParams.includeStatistics) {
      statistics = await historyRepairRepository.getRepairStatistics(
        queryParams.customerId
      );
    }

    // Limit recent activity if specified
    if (queryParams.includeRecentActivity && overview.recentActivity) {
      overview.recentActivity = overview.recentActivity.slice(0, queryParams.activityLimit);
    }

    return NextResponse.json({
      success: true,
      data: {
        overview,
        statistics,
      },
    });
  } catch (error) {
    console.error('Error fetching history overview:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid query parameters',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch history overview',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/history/overview/dashboard
 * Get dashboard statistics for history overview
 */
async function getDashboardOverview(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get overall statistics
    const [
      totalCustomersWithHistory,
      totalRepairs,
      totalMaintenance,
      totalComplaints,
      recentRepairs,
      recentActivity
    ] = await Promise.all([
      historyCardRepository.count(),
      historyRepairRepository.count(),
      historyCardRepository.getPrismaClient().historyMaintenance.count(),
      historyCardRepository.getPrismaClient().historyComplaint.count(),
      historyRepairRepository.findRecentRepairs({ limit: 5 }),
      historyCardRepository.getPrismaClient().historyRepair.findMany({
        take: 10,
        orderBy: { repairDate: 'desc' },
        include: {
          historyCard: {
            include: {
              customer: {
                select: {
                  id: true,
                  name: true,
                }
              }
            }
          }
        }
      })
    ]);

    // Get monthly statistics for the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    // Get monthly repair counts for the last 6 months
    const monthlyRepairs = await historyRepairRepository.getPrismaClient().historyRepair.findMany({
      where: {
        repairDate: {
          gte: sixMonthsAgo
        }
      },
      select: {
        repairDate: true
      }
    });

    // Process monthly stats into a more usable format
    const monthlyData = monthlyRepairs.reduce((acc, repair) => {
      if (repair.repairDate) {
        const month = repair.repairDate.toISOString().slice(0, 7); // YYYY-MM format
        acc[month] = (acc[month] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      data: {
        summary: {
          totalCustomersWithHistory,
          totalRepairs,
          totalMaintenance,
          totalComplaints,
          recentRepairsCount: recentRepairs.length,
        },
        recentRepairs: recentRepairs.map(repair => ({
          id: repair.id,
          description: repair.description,
          repairDate: repair.repairDate,
          customer: repair.historyCard?.customer?.name,
        })),
        recentActivity: recentActivity.map(repair => ({
          id: repair.id,
          type: 'REPAIR',
          date: repair.repairDate,
          description: repair.description,
          customer: repair.historyCard?.customer?.name,
        })),
        monthlyData,
      },
    });
  } catch (error) {
    console.error('Error fetching dashboard overview:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch dashboard overview',
      },
      { status: 500 }
    );
  }
}

// Export handlers with role protection and activity logging
export const GET = ActivityLoggerMiddlewareFactory.forHistoryRoutes(
  withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE', 'USER'], async (request: NextRequest) => {
    const { searchParams } = new URL(request.url);
    const isDashboard = searchParams.get('dashboard') === 'true';
    
    if (isDashboard) {
      return getDashboardOverview(request);
    } else {
      return getHistoryOverview(request);
    }
  }),
  {
    action: 'view_history_overview',
    getEntityId: (req) => {
      try {
        const url = new URL(req.url);
        return url.searchParams.get('customerId') || 'dashboard';
      } catch {
        return 'unknown';
      }
    },
  }
);
