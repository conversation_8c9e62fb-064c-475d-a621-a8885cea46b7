import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getHistoryCardRepository } from '@/lib/repositories';
import { ActivityLoggerMiddlewareFactory } from '@/lib/middleware/activity-logger.middleware';

// Validation schema for export parameters
const exportQuerySchema = z.object({
  format: z.enum(['csv', 'excel']).default('csv'),
  customerId: z.string().uuid().optional(),
  source: z.enum(['AMC', 'INW', 'OTW']).optional(),
  amcId: z.string().uuid().optional(),
  inWarrantyId: z.string().uuid().optional(),
  outWarrantyId: z.string().uuid().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  includeDetails: z.coerce.boolean().default(false),
});

/**
 * GET /api/history-cards/export
 * Export history cards data in CSV or Excel format
 */
async function exportHistoryCards(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    // Validate query parameters
    const validatedQuery = exportQuerySchema.parse(queryParams);
    
    const historyCardRepository = getHistoryCardRepository();
    
    // Build where clause for filtering
    const where: any = {};
    
    if (validatedQuery.customerId) {
      where.customerId = validatedQuery.customerId;
    }
    
    if (validatedQuery.source) {
      where.source = validatedQuery.source;
    }
    
    if (validatedQuery.amcId) {
      where.amcId = validatedQuery.amcId;
    }
    
    if (validatedQuery.inWarrantyId) {
      where.inWarrantyId = validatedQuery.inWarrantyId;
    }
    
    if (validatedQuery.outWarrantyId) {
      where.outWarrantyId = validatedQuery.outWarrantyId;
    }
    
    if (validatedQuery.dateFrom || validatedQuery.dateTo) {
      where.createdAt = {};
      if (validatedQuery.dateFrom) {
        where.createdAt.gte = new Date(validatedQuery.dateFrom);
      }
      if (validatedQuery.dateTo) {
        where.createdAt.lte = new Date(validatedQuery.dateTo);
      }
    }
    
    // Get history cards with relations
    const historyCards = await (historyCardRepository as any).model.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            address: true,
            city: true,
            phone: true,
          },
        },
        sections: validatedQuery.includeDetails ? {
          select: {
            id: true,
            sectionCode: true,
            content: true,
            createdAt: true,
          },
          orderBy: {
            sectionCode: 'asc',
          },
        } : false,
      },
    });
    
    if (validatedQuery.format === 'csv') {
      // Generate CSV content
      const csvHeaders = [
        'ID',
        'Card Number',
        'Customer Name',
        'Customer Address',
        'Customer City',
        'Customer Phone',
        'Source',
        'AMC ID',
        'In-Warranty ID',
        'Out-Warranty ID',
        'To Card Number',
        'Original ID',
        'Created At',
        'Updated At',
      ];
      
      if (validatedQuery.includeDetails) {
        csvHeaders.push('Sections Count', 'Section Codes');
      }
      
      const csvRows = historyCards.map(card => {
        const row = [
          card.id,
          card.cardNo || '',
          card.customer?.name || '',
          card.customer?.address || '',
          card.customer?.city || '',
          card.customer?.phone || '',
          card.source || '',
          card.amcId || '',
          card.inWarrantyId || '',
          card.outWarrantyId || '',
          card.toCardNo || '',
          card.originalId || '',
          card.createdAt.toISOString(),
          card.updatedAt.toISOString(),
        ];
        
        if (validatedQuery.includeDetails && card.sections) {
          row.push(
            card.sections.length.toString(),
            card.sections.map(s => s.sectionCode).join('; ')
          );
        }
        
        return row;
      });
      
      const csvContent = [csvHeaders, ...csvRows]
        .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
        .join('\n');
      
      const filename = `history-cards-${new Date().toISOString().split('T')[0]}.csv`;
      
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}"`,
        },
      });
    } else {
      // For Excel format, return JSON data that can be processed by frontend
      // This is a simplified approach - in production, you might want to use a library like xlsx
      const excelData = historyCards.map(card => ({
        ID: card.id,
        'Card Number': card.cardNo || '',
        'Customer Name': card.customer?.name || '',
        'Customer Address': card.customer?.address || '',
        'Customer City': card.customer?.city || '',
        'Customer Phone': card.customer?.phone || '',
        Source: card.source || '',
        'AMC ID': card.amcId || '',
        'In-Warranty ID': card.inWarrantyId || '',
        'Out-Warranty ID': card.outWarrantyId || '',
        'To Card Number': card.toCardNo || '',
        'Original ID': card.originalId || '',
        'Created At': card.createdAt.toISOString(),
        'Updated At': card.updatedAt.toISOString(),
        ...(validatedQuery.includeDetails && card.sections ? {
          'Sections Count': card.sections.length,
          'Section Codes': card.sections.map(s => s.sectionCode).join('; '),
        } : {}),
      }));
      
      return NextResponse.json({
        success: true,
        data: excelData,
        filename: `history-cards-${new Date().toISOString().split('T')[0]}.xlsx`,
      });
    }
  } catch (error) {
    console.error('Error exporting history cards:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid export parameters',
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to export history cards',
      },
      { status: 500 }
    );
  }
}

// Export handler with role protection and activity logging
export const GET = ActivityLoggerMiddlewareFactory.forHistoryCardRoutes(
  withRoleProtection(['ADMIN', 'MANAGER', 'EXECUTIVE'], exportHistoryCards),
  {
    action: 'export_history_cards',
    getEntityId: () => 'export',
  }
);
